<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Video Downloader</string>
    <string name="browser_search_hint">Paste Video Site URL</string>

    <string name="download_task_new">Create New Download Task</string>
    <string name="download_task_new_filename">File Name</string>
    <string name="download_task_new_storage_path">Storage Path</string>
    <string name="download_task_new_threads_number">number of download threads</string>
    <string name="download_task_new_threads_auto">automatic</string>

    <string name="download_dir_hidden_hint">The files in the hidden folder will not be displayed in the system album and system file manager.</string>


    <string name="download_task_no_video_hint">You have not downloaded any videos yet</string>
    <string name="download_task_go_browser">Browser search and play video</string>
    <string name="download_task_management">Download management</string>
    <string name="download_task_downloading">Downloading</string>
    <string name="download_task_downloaded">Downloaded</string>
    <string name="download_task_already_exists">Task Already Exists</string>
    <string name="download_task_download_start">Download</string>


    <string name="download_dir_hidden">Hidden Folder</string>
    <string name="menu_move_in">Move In</string>
    <string name="menu_move_out">Move Out</string>

    <string name="menu_file_delete">File Delete</string>

    <string name="time_now">Now</string>
    <string name="time_an_minute_ago">A minute ago</string>
    <string name="time_minutes_ago">minutes ago</string>
    <string name="time_an_hour_ago">An hour ago</string>
    <string name="time_hours_ago">hours ago</string>
    <string name="time_yesterday">Yesterday</string>
    <string name="time_days_ago">days ago</string>

    <string name="subscribed_successfully">Already subscribed successfully</string>
    <string name="subscribe_start">Subscribe</string>
    <string name="subscribe_des">Our app is completely free to use, but we also offer an optional subscription service. By subscribing to our service, you can enjoy an ad-free experience and get faster download speeds. You can cancel your subscription at any time, and after cancellation, you will no longer enjoy the ad-free and faster download services.</string>
    <string name="subscribe_period">Subscription Period</string>

    <string name="home_share_app">Share App</string>
    <string name="home_video_tutorial">View video download tutorial</string>
    <string name="home_video_tutorial_title">Video download tutorial</string>
    <string name="home_video_tutorial_1">1.Browse video websites</string>
    <string name="home_video_tutorial_2">2.Play video on web page</string>
    <string name="home_video_tutorial_3">3.Video will be detected automatically</string>

    <string name="book_mark_added">Bookmarked</string>
    <string name="book_mark_removed">Bookmark removed</string>
    <string name="book_mark_open_in_new_tab">Open in new tab</string>
    <string name="book_mark_copy_link">Copy link</string>
    <string name="book_mark_remove">Remove bookmark</string>
    <string name="book_mark_edit">Edit bookmark</string>
    <string name="book_mark_save">Save bookmark</string>
    <string name="book_mark_edit_title">Edit Title</string>

    <string name="browser_clear_history">Clear Browser History</string>
    <string name="tab_new">Create New Tab</string>
    <string name="menu_convert_to_audio">Video Convert To Audio</string>
    <string name="tab_music">Music</string>

    <string name="ad_remove_ad_for_free">Remove ads for free.</string>
    <string name="ad_remove_ad_for_minute">Watch the advertisement in full, remove ads for free for %1$s minutes.</string>
    <string name="ad_remove_ad_failed">Failed to receive the reward because the advertisement was not fully viewed..</string>

    <string name="upgrade_title">Upgrade tips</string>
    <string name="upgrade_des">A new version is available, do you want to update now?</string>
    <string name="upgrade_go">update</string>
    <string name="upgrade_later">later</string>

    <!-- VIP Subscription Strings -->
    <string name="vip_status_subscribed">VIP Member</string>
    <string name="vip_status_not_subscribed">Become VIP</string>
    <string name="vip_subscription_entrance_hint">Tap to unlock premium features</string>
    <string name="vip_subscription_title">VIP Subscription</string>
    <string name="vip_subscription_benefits_title">VIP Benefits</string>
    <string name="vip_benefit_no_ads">Ad-free experience</string>
    <string name="vip_benefit_high_speed">High-speed downloads</string>
    <string name="vip_benefit_priority_support">Priority customer support</string>
    <string name="vip_benefit_exclusive_features">Exclusive features</string>
    <string name="vip_subscription_policy_title">Subscription Terms</string>
    <string name="vip_subscription_policy_description">• Payment will be charged to your Google Play account at confirmation of purchase\n• Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period\n• Account will be charged for renewal within 24 hours prior to the end of the current period\n• You can manage and cancel your subscriptions by going to your account settings on the Google Play Store after purchase\n• Any unused portion of a free trial period will be forfeited when you purchase a subscription</string>
    <string name="vip_subscription_management_info">You can manage your subscription anytime in Google Play Store settings.</string>
    <string name="vip_free_usage_title">Free Usage</string>
    <string name="vip_free_usage_description">You can continue using the app for free with basic features. VIP subscription is optional and provides additional premium features.</string>
    <string name="vip_continue_free">Continue Free</string>
    <string name="vip_subscription_management_title">Subscription Management</string>
    <string name="vip_subscription_management_description">Manage your subscription, view billing history, or cancel anytime through Google Play Store.</string>
    <string name="vip_manage_subscription">Manage Subscription</string>
    <string name="vip_auto_renewal_notice">Subscription will automatically renew unless cancelled at least 24 hours before the end of the current period.</string>
    <string name="vip_loading_products">Loading subscription plans...</string>
    <string name="vip_load_products_failed">Failed to load subscription plans. Please try again later.</string>
    <string name="vip_dialog_close">Close</string>
    <string name="vip_test_subscription">Test Subscription</string>
    <string name="vip_subscribe_test">Subscribe (Test)</string>
    <string name="vip_subscription_plan">Subscription Plan</string>
    <string name="vip_subscription_period">Period</string>

    <!-- Subscription Status Strings -->
    <string name="subscription_status_subscribed">✓ 已订阅</string>
    <string name="subscription_status_not_subscribed">✗ 未订阅</string>
    <string name="subscription_expiration_time">到期时间</string>
    <string name="subscription_remaining_days">剩余 %1$d 天</string>
    <string name="subscription_no_expiration_info">暂无到期时间信息</string>

    <!-- History Item Strings -->
    <string name="history_no_title">无标题</string>
    <string name="history_no_link">无链接</string>

</resources>