package free.download.video.downloader.ui.screens.home

import CoroutineTask
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.android.billingclient.api.ProductDetails
import com.lib.bill.manager.BillIntent
import com.lib.bill.manager.BillState
import com.lib.bill.manager.BillingRepo
import com.lib.bill.manager.BillingTools
import com.lib.bill.manager.BillingViewModel
import com.lib.bill.manager.bean.AppProductDetails
import com.tiny.ad.network.AdRepo
import com.tiny.domain.util.BitmapCacheUtil
import com.tiny.domain.util.copyToClipboard
import com.tiny.domain.util.forEachGroup
import com.tinypretty.component.GlobalModule
import com.tinypretty.component.GlobalModule.activity
import com.tinypretty.component.GlobalModule.newLog
import com.tinypretty.component.ResTools
import com.tinypretty.component.urlHostIconCacheKey
import com.tinypretty.ui.componets.AppButton
import com.tinypretty.ui.componets.ImageApp
import com.tinypretty.ui.componets.RowSplit
import com.tinypretty.ui.componets.SpacerFix
import com.tinypretty.ui.componets.ad.SelfAdBannerView
import com.tinypretty.ui.dialogs.AlertCloseAbleContent
import com.tinypretty.ui.theme.C
import com.tinypretty.ui.theme.MT
import com.tinypretty.ui.theme.clip
import com.tinypretty.ui.theme.mid
import free.download.video.downloader.LocalAppViewModel
import free.download.video.downloader.LocalBillingViewModel
import free.download.video.downloader.LocalBookmarkViewModel
import free.download.video.downloader.LocalHistoryViewModel
import free.download.video.downloader.R
import free.download.video.downloader.model.bookmark.BookMarkEntity
import free.download.video.downloader.model.history.HistoryEntity
import free.download.video.downloader.ui.components.RewardNoADModeScreen
import free.download.video.downloader.ui.screens.downloader.downloadGuideItem

/**
 * <AUTHOR>
 * @Since 2024/01/12
 */
@Composable
fun HomePageScreen() = Box(
    Modifier
        .fillMaxSize()
        .background(MT.color.background)
) {
    val log = newLog("HomePageScreen").apply { i { "HomePageScreen" } }
    val bookMarks = LocalBookmarkViewModel.current.bookMarkList.collectAsState(initial = emptyList())
    val historyViewModel = LocalHistoryViewModel.current
    val history = historyViewModel.history.collectAsState(initial = emptyList())

    val listState = rememberLazyListState()

    // 内容区域（可滚动）
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = 80.dp) // 为底部 VIP 留出空间
            .background(MT.color.background),
        state = listState
    ) {
        if (bookMarks.value.isNotEmpty()) {
            item {
                Column(
                    Modifier
                        .background(MT.color.surface, MT.shapes.small)
                        .padding(horizontal = 0.dp, vertical = 12.dp)
                ) {
                    bookMarks.value.sortedBy { it.url }.forEachGroup(5) { items ->
                        RowSplit(columnCount = 5) { index ->
                            items.getOrNull(index)?.let { item ->
                                log.i { "HomePageScreen bookmark $item" }
                                BookMarkItem(item)
                            }
                        }
                    }
                }
            }
        }

        item {
            Box(contentAlignment = Alignment.CenterEnd, modifier = Modifier.fillMaxWidth()) {
                RewardNoADModeScreen(
                    Modifier
                        .padding(top = 12.dp)
                        .align(Alignment.CenterStart)
                )

                if (history.value.isNotEmpty()) {
                    ImageApp(
                        data = "res/ic_clear_all.webp",
                        Modifier
                            .size(32.dp)
                            .padding(6.dp)
                            .clickable {
                                CoroutineTask("clearHistory")
                                    .io()
                                    .launch {
                                        historyViewModel.clearHistory()
                                    }
                            },
                        color = MT.color.onBackground
                    )
                }
            }
        }

        val validHistory = history.value.filter { it != null && it.isValid() }.asReversed()
        validHistory.forEachIndexed { index, item ->
            item {
                HistoryItem(item)
            }
        }

        item {
            12.SpacerFix()
        }

        downloadGuideItem()
    }

    // 固定在底部的 VIP 状态栏
    VipStatusItem(
        modifier = Modifier
            .align(Alignment.BottomCenter)
            .fillMaxWidth()
            .background(MT.color.surface)
            .padding(12.dp)
    )
}

@Composable
private fun HistoryItem(it: HistoryEntity?) {
    if (it == null || !it.isValid()) {
        return
    }

    val appVM = LocalAppViewModel.current
    Row(
        Modifier
            .fillMaxWidth()
            .padding(bottom = 24.dp)
            .clickable { appVM.toBrowserPage(it.url) }, verticalAlignment = Alignment.CenterVertically
    ) {
        ImageApp(
            data = "res/ic_bottom_history.webp", modifier = Modifier
                .size(42.dp)
                .padding(6.dp), color = MT.color.onSurface
        )
        Column(Modifier.padding(start = 12.dp)) {
            Text(
                text = it.title.ifBlank { ResTools.str(R.string.history_no_title) },
                style = MT.typography.titleMedium,
                color = MT.color.onSurface,
                maxLines = 1
            )
            Text(
                text = it.url.ifBlank { ResTools.str(R.string.history_no_link) },
                style = MT.typography.bodyMedium,
                color = MT.color.onSurface,
                maxLines = 1
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BookMarkItem(bookMarkEntity: BookMarkEntity) {
    val activity = activity() ?: return
    val bookMarksVM = LocalBookmarkViewModel.current
    val cachedIconUrl = BitmapCacheUtil.getCachedPath(bookMarkEntity.url.urlHostIconCacheKey())
    val iconUrl = remember { mutableStateOf(cachedIconUrl ?: bookMarkEntity.icon) }

    val appVM = LocalAppViewModel.current
    val showSelected = remember { mutableStateOf(false) }
    val showEdit = remember { mutableStateOf(false) }


    if (showEdit.value) {
        val text = remember { mutableStateOf(bookMarkEntity.title) }
        AlertDialog(
            onDismissRequest = { showEdit.value = false },
            title = { Text(ResTools.str(R.string.book_mark_edit)) },
            text = {
                TextField(
                    value = text.value,
                    onValueChange = { text.value = it },
                    label = { Text(ResTools.str(R.string.book_mark_edit_title)) }
                )
            },
            confirmButton = {
                AppButton(msg = ResTools.str(R.string.book_mark_save)) {
                    CoroutineTask("updateBookmark").io().launch {
                        bookMarkEntity.title = text.value
                        bookMarksVM.update(bookMarkEntity)
                        showEdit.value = false
                    }
                }
            }
        )
    }

//    AlertCloseAbleContent(state = showEdit) {
//        Column {
//            val resultValue = remember { mutableStateOf(bookMarkEntity.title) }
//            TextField(value = resultValue.value, onValueChange = {
//                resultValue.value = it
//            })
//
//            AppButton(msg = ResTools.str(R.string.book_mark_save)) {
//                CoroutineTask("updateBookmark").io().launch {
//                    bookMarkEntity.title = resultValue.value
//                    bookMarksVM.update(bookMarkEntity)
//                    showEdit.value = false
//                }
//            }
//        }
//    }

    AlertCloseAbleContent(state = showSelected) {
        Column(Modifier.background(MT.C().background)) {
            AppButton(ResTools.str(R.string.book_mark_open_in_new_tab)) {
                appVM.toBrowserPage(bookMarkEntity.url)
                showSelected.value = false
            }

            AppButton(msg = ResTools.str(R.string.book_mark_copy_link)) {
                copyToClipboard(activity, bookMarkEntity.url)
                showSelected.value = false
            }

            AppButton(msg = ResTools.str(R.string.book_mark_remove)) {
                CoroutineTask("deleteBookmark").io().launch {
                    bookMarksVM.deleteBookmark(bookMarkEntity.url)
                }
                showSelected.value = false
            }

            AppButton(msg = ResTools.str(R.string.book_mark_edit)) {
                showSelected.value = false
                showEdit.value = true
            }
        }
    }
    Column(
        Modifier
            .fillMaxWidth()
            .padding(top = 6.dp), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ImageApp(data = iconUrl.value, contentScale = ContentScale.Fit,
            modifier = Modifier
                .size(36.dp)
                .padding()
                .clip(CircleShape)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = { appVM.toBrowserPage(bookMarkEntity.url) },
                        onLongPress = { showSelected.value = true }
                    )
                }
        )
        Text(
            text = bookMarkEntity.title,
            Modifier
                .fillMaxWidth()
                .padding(top = 6.dp), maxLines = 2, color = MT.color.onSurface, style = MT.typography.labelSmall, textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun VipStatusItem(modifier: Modifier) {
    val billingViewModel = LocalBillingViewModel.current
    val isSubscribed = BillingRepo.subscribedStateFlow.collectAsState()
    val showVipDialog = remember { mutableStateOf(false) }
    val showSubscriptionDialog = remember { mutableStateOf(false) }

    // 固定显示VIP区域 - 已订阅显示状态，未订阅显示订阅入口
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp, horizontal = 12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSubscribed.value)
                Color(0xFFFFD700).copy(alpha = 0.1f)
            else
                MT.color.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .clickable {
                    if (isSubscribed.value) {
                        showVipDialog.value = true
                    } else {
                        showSubscriptionDialog.value = true
                    }
                },
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // VIP图标
            ImageApp(
                data = "res/ic_vip.webp",
                modifier = Modifier.size(32.dp),
                color = if (isSubscribed.value) Color(0xFFFFD700) else MT.color.onSurface.copy(alpha = 0.6f)
            )
            12.SpacerFix()

            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = if (isSubscribed.value)
                        ResTools.str(R.string.vip_status_subscribed)
                    else
                        ResTools.str(R.string.vip_status_not_subscribed),
                    style = MT.typography.titleMedium,
                    color = if (isSubscribed.value) Color(0xFFFFD700) else MT.color.onSurface,
                    fontWeight = FontWeight.Bold
                )

                if (!isSubscribed.value) {
                    4.SpacerFix()
                    Text(
                        text = ResTools.str(R.string.vip_subscription_entrance_hint),
                        style = MT.typography.bodySmall,
                        color = MT.color.onSurface.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }

    // VIP状态详情对话框（已订阅用户）
    if (showVipDialog.value) {
        VipStatusDialog(
            onDismiss = { showVipDialog.value = false }
        )
    }

    // 订阅对话框（未订阅用户）
    if (showSubscriptionDialog.value) {
        SubscriptionDialog(
            billingViewModel = billingViewModel,
            onDismiss = { showSubscriptionDialog.value = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun VipStatusDialog(onDismiss: () -> Unit) {
    val subscriptionStatus = remember { BillingRepo.getSubscriptionStatus() }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.fillMaxWidth()
            ) {
                ImageApp(
                    data = "res/ic_vip.webp",
                    modifier = Modifier.size(24.dp),
                    color = Color(0xFFFFD700)
                )
            }
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 订阅状态
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = if (subscriptionStatus.isSubscribed)
                            Color(0xFF4CAF50).copy(alpha = 0.1f)
                        else
                            Color(0xFFF44336).copy(alpha = 0.1f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = if (subscriptionStatus.isSubscribed)
                                ResTools.str(R.string.subscription_status_subscribed)
                            else
                                ResTools.str(R.string.subscription_status_not_subscribed),
                            style = MT.typography.titleMedium,
                            color = if (subscriptionStatus.isSubscribed)
                                Color(0xFF4CAF50)
                            else
                                Color(0xFFF44336),
                            fontWeight = FontWeight.Bold
                        )

                        8.SpacerFix()

                        // 到期时间信息
                        if (subscriptionStatus.expirationDateString != null) {
                            Text(
                                text = ResTools.str(R.string.subscription_expiration_time),
                                style = MT.typography.bodyMedium,
                                color = MT.color.onSurface.copy(alpha = 0.7f)
                            )
                            4.SpacerFix()
                            Text(
                                text = subscriptionStatus?.expirationDateString?:"",
                                style = MT.typography.titleMedium,
                                color = MT.color.onSurface,
                                fontWeight = FontWeight.Medium
                            )

                            // 显示剩余天数
                            subscriptionStatus.expirationTimestamp?.let { expirationTime ->
                                val currentTime = System.currentTimeMillis()
                                val remainingDays = ((expirationTime - currentTime) / (24 * 60 * 60 * 1000)).toInt()
                                if (remainingDays > 0) {
                                    4.SpacerFix()
                                    Text(
                                        text = ResTools.str(R.string.subscription_remaining_days, remainingDays),
                                        style = MT.typography.bodySmall,
                                        color = if (remainingDays <= 7) Color(0xFFFF9800) else Color(0xFF4CAF50)
                                    )
                                }
                            }
                        } else {
                            Text(
                                text = ResTools.str(R.string.subscription_no_expiration_info),
                                style = MT.typography.bodyMedium,
                                color = MT.color.onSurface.copy(alpha = 0.5f)
                            )
                        }
                    }
                }

                // VIP特权说明
                if (subscriptionStatus.isSubscribed) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MT.color.surface
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = ResTools.str(R.string.vip_subscription_benefits_title),
                                style = MT.typography.titleMedium,
                                color = MT.color.onSurface,
                                fontWeight = FontWeight.Bold
                            )
                            8.SpacerFix()

                            val privileges = listOf(
                                ResTools.str(R.string.vip_benefit_no_ads),
                                ResTools.str(R.string.vip_benefit_high_speed),
                                ResTools.str(R.string.vip_benefit_priority_support),
                                ResTools.str(R.string.vip_benefit_exclusive_features)
                            )

                            privileges.forEach { privilege ->
                                Text(
                                    text = "✓ $privilege",
                                    style = MT.typography.bodyMedium,
                                    color = Color(0xFF4CAF50),
                                    modifier = Modifier.padding(vertical = 2.dp)
                                )
                            }
                        }
                    }
                }

                // 订阅管理
                if (subscriptionStatus.isSubscribed) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFF2196F3).copy(alpha = 0.1f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = ResTools.str(R.string.vip_subscription_management_title),
                                style = MT.typography.titleMedium,
                                color = MT.color.onSurface,
                                fontWeight = FontWeight.Bold
                            )
                            8.SpacerFix()
                            Text(
                                text = ResTools.str(R.string.vip_subscription_management_description),
                                style = MT.typography.bodySmall,
                                color = MT.color.onSurface.copy(alpha = 0.8f)
                            )
                            12.SpacerFix()
                            AppButton(
                                msg = ResTools.str(R.string.vip_manage_subscription),
                                onClick = {
                                    // 打开Google Play订阅管理页面
                                    val activity = GlobalModule.activity()
                                    activity?.let { act ->
                                        try {
                                            val intent = Intent(Intent.ACTION_VIEW).apply {
                                                data = Uri.parse("https://play.google.com/store/account/subscriptions")
                                                setPackage("com.android.vending")
                                            }
                                            act.startActivity(intent)
                                        } catch (e: Exception) {
                                            // 如果Google Play不可用，打开网页版
                                            val webIntent = Intent(Intent.ACTION_VIEW).apply {
                                                data = Uri.parse("https://play.google.com/store/account/subscriptions")
                                            }
                                            act.startActivity(webIntent)
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            AppButton(msg = ResTools.str(com.tiny.compose.ui.R.string.dlg_confirm)) {
                onDismiss()
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SubscriptionDialog(
    billingViewModel: BillingViewModel,
    onDismiss: () -> Unit
) {
    val purchaseState = billingViewModel.state.collectAsState().value

    LaunchedEffect(Unit) {
        billingViewModel.process(BillIntent.LoadProducts("home_subscription"))
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.fillMaxWidth()
            ) {
                ImageApp(
                    data = "res/ic_vip.webp",
                    modifier = Modifier.size(24.dp),
                    color = Color(0xFFFFD700)
                )
                8.SpacerFix()
                Text(
                    text = ResTools.str(R.string.vip_subscription_title),
                    style = MT.typography.titleLarge,
                    color = Color(0xFFFFD700),
                    fontWeight = FontWeight.Bold
                )
            }
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 订阅说明 - 符合Google Play政策
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MT.color.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = ResTools.str(R.string.vip_subscription_benefits_title),
                            style = MT.typography.titleMedium,
                            color = MT.color.onSurface,
                            fontWeight = FontWeight.Bold
                        )
                        8.SpacerFix()

                        val benefits = listOf(
                            ResTools.str(R.string.vip_benefit_no_ads),
                            ResTools.str(R.string.vip_benefit_high_speed),
                            ResTools.str(R.string.vip_benefit_priority_support),
                            ResTools.str(R.string.vip_benefit_exclusive_features)
                        )

                        benefits.forEach { benefit ->
                            Text(
                                text = "✓ $benefit",
                                style = MT.typography.bodyMedium,
                                color = Color(0xFF4CAF50),
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    }
                }

                // 免费使用说明
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF4CAF50).copy(alpha = 0.1f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = ResTools.str(R.string.vip_free_usage_title),
                            style = MT.typography.titleSmall,
                            color = MT.color.onSurface,
                            fontWeight = FontWeight.Bold
                        )
                        4.SpacerFix()
                        Text(
                            text = ResTools.str(R.string.vip_free_usage_description),
                            style = MT.typography.bodySmall,
                            color = MT.color.onSurface.copy(alpha = 0.8f)
                        )
                    }
                }

                // Google Play订阅政策说明
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF2196F3).copy(alpha = 0.1f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = ResTools.str(R.string.vip_subscription_policy_title),
                            style = MT.typography.titleSmall,
                            color = MT.color.onSurface,
                            fontWeight = FontWeight.Bold
                        )
                        4.SpacerFix()
                        Text(
                            text = ResTools.str(R.string.vip_subscription_policy_description),
                            style = MT.typography.bodySmall,
                            color = MT.color.onSurface.copy(alpha = 0.8f)
                        )
                        8.SpacerFix()
                        Text(
                            text = ResTools.str(R.string.vip_subscription_management_info),
                            style = MT.typography.bodySmall,
                            color = Color(0xFF2196F3),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                // 订阅商品列表
                when (purchaseState) {
                    is BillState.Loading -> {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(100.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = ResTools.str(R.string.vip_loading_products),
                                style = MT.typography.bodyMedium,
                                color = MT.color.onSurface.copy(alpha = 0.6f)
                            )
                        }
                    }

                    is BillState.ProductsLoadSucceed -> {
                        purchaseState.products.forEach { product ->
                            SubscriptionProductItem(
                                product = product,
                                onSubscribe = { productDetails, offer ->
                                    val pd = productDetails.productDetails
                                    val billingPeriod = offer?.pricingPhases?.pricingPhaseList?.first()?.billingPeriod ?: ""
                                    billingViewModel.billingPeriod = billingPeriod
                                    billingViewModel.process(BillIntent.PurchaseClick("home_subscription", pd, offer))
                                    onDismiss()
                                }
                            )
                        }
                    }

                    is BillState.ProductsLoadFailed -> {
                        Text(
                            text = ResTools.str(R.string.vip_load_products_failed),
                            style = MT.typography.bodyMedium,
                            color = Color(0xFFF44336),
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(16.dp)
                        )
                    }

                    else -> {
                        // Idle state
                    }
                }
            }
        },
        confirmButton = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                AppButton(
                    msg = ResTools.str(R.string.vip_continue_free),
                    modifier = Modifier.weight(1f),
                    onClick = onDismiss
                )
                8.SpacerFix()
                AppButton(
                    msg = ResTools.str(R.string.vip_dialog_close),
                    modifier = Modifier.weight(1f),
                    onClick = onDismiss
                )
            }
        }
    )
}

@Composable
private fun SubscriptionProductItem(
    product: AppProductDetails,
    onSubscribe: (AppProductDetails, ProductDetails.SubscriptionOfferDetails?) -> Unit
) {
    val allOffers = product.productDetails?.subscriptionOfferDetails

    if (allOffers == null) {
        // Debug模式下的测试商品
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFFFD700).copy(alpha = 0.1f)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = ResTools.str(R.string.vip_test_subscription),
                    style = MT.typography.titleMedium,
                    color = MT.color.onSurface,
                    fontWeight = FontWeight.Bold
                )
                8.SpacerFix()
                AppButton(
                    msg = ResTools.str(R.string.vip_subscribe_test),
                    onClick = { onSubscribe(product, null) }
                )
            }
        }
        return
    }

    // 真实的订阅商品
    product.productDetails?.subscriptionOfferDetails?.forEach { offer ->
        val pricingPhase = offer?.pricingPhases?.pricingPhaseList?.first() ?: return
        val formattedPrice = pricingPhase.formattedPrice
        val billingPeriod = pricingPhase.billingPeriod
        val startEndTime = BillingTools.getStartEndTime(billingPeriod)

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFFFD700).copy(alpha = 0.1f)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = ResTools.str(R.string.vip_subscription_plan),
                    style = MT.typography.titleMedium,
                    color = MT.color.onSurface,
                    fontWeight = FontWeight.Bold
                )
                4.SpacerFix()
                Text(
                    text = "${ResTools.str(R.string.vip_subscription_period)}: ${startEndTime.first} - ${startEndTime.second}",
                    style = MT.typography.bodyMedium,
                    color = MT.color.onSurface.copy(alpha = 0.8f)
                )
                12.SpacerFix()
                AppButton(
                    msg = "$formattedPrice ${ResTools.str(R.string.subscribe_start)}",
                    onClick = { onSubscribe(product, offer) }
                )
            }
        }
    }
}